import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from slr_network import SLRModel
import utils
from collections import OrderedDict

def visualize_attention_params(model_weights, dataset='phoenix2014-T', 
                              window_sizes=[7, 9, 11], 
                              kernel_sets=[[3, 5, 7], [5, 9, 13]],
                              reduction_ratios=[8, 16], 
                              out_dir='./param_vis'):
    """可视化不同参数设置下的动态时空注意力效果"""
    if not os.path.exists(out_dir):
        os.makedirs(out_dir)
    
    # 加载数据字典
    dict_path = f'./preprocess/{dataset}/gloss_dict.npy'
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    
    # 设置设备
    device = utils.GpuDataParallel()
    device.set_device(0)
    
    # 创建结果图表
    fig, axes = plt.subplots(len(window_sizes), len(kernel_sets), figsize=(15, 10))
    
    for i, max_window_size in enumerate(window_sizes):
        for j, kernel_sizes in enumerate(kernel_sets):
            # 创建模型
            model = SLRModel(
                num_classes=len(gloss_dict)+1,
                c2d_type='resnet18',
                conv_type=2,
                use_bn=1,
                gloss_dict=gloss_dict,
                loss_weights={'ConvCTC': 1.0, 'SeqCTC': 1.0, 'Dist': 25.0},
                attention_params={
                    'max_window_size': max_window_size,
                    'kernel_sizes': kernel_sizes,
                    'reduction_ratio': reduction_ratios[0]
                }
            )
            
            # 加载权重
            state_dict = torch.load(model_weights)['model_state_dict']
            state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
            model.load_state_dict(state_dict, strict=False)  # 使用strict=False允许参数不完全匹配
            
            # 生成示例图像并保存
            title = f"Window={max_window_size}, Kernels={kernel_sizes}"
            axes[i, j].set_title(title)
            axes[i, j].axis('off')
            
            # 这里可以添加代码来生成实际的注意力可视化
            # 为简化示例，这里只显示参数配置
            axes[i, j].text(0.5, 0.5, title, ha='center', va='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, 'attention_params_comparison.png'))
    print(f"参数比较图已保存到 {out_dir}")

# 使用示例
visualize_attention_params(
    model_weights='./pretrain_model/dev_18.90_PHOENIX14-T.pt',
    window_sizes=[7, 9, 11],
    kernel_sets=[[3, 5, 7], [5, 9, 13]],
    reduction_ratios=[16]
)