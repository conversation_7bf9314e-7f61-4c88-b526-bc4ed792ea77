# 动态时间注意力机制最佳超参数配置

## 最佳配置
max_window_size = 11 (最大窗口大小)
kernel_sizes = 5,9,13 (卷积核大小)
reduction_ratio = 16 (通道缩减比例)
dev_wer = 177.37% (词错误率)
time = 53.66s (运行时间)

## 各超参数的平均WER

### 1. 最大窗口大小 (max_window_size)
   11: 193.94%
   7: 195.87%
   9: 198.26%

### 2. 卷积核大小 (kernel_sizes)
   5,9,13: 195.12%
   3,5,7: 196.18%
   3,7,11: 196.76%

### 3. 通道缩减比例 (reduction_ratio)
   16: 193.27%
   32: 198.50%
   8: 196.30%

## 所有测试的配置
1. max_window_size=11, kernel_sizes=5,9,13, reduction_ratio=16, dev_wer=177.37%, time=53.66s
2. max_window_size=11, kernel_sizes=3,5,7, reduction_ratio=16, dev_wer=180.55%, time=54.65s
3. max_window_size=7, kernel_sizes=3,7,11, reduction_ratio=8, dev_wer=187.19%, time=53.82s
4. max_window_size=7, kernel_sizes=5,9,13, reduction_ratio=8, dev_wer=195.57%, time=53.68s
5. max_window_size=7, kernel_sizes=5,9,13, reduction_ratio=16, dev_wer=195.73%, time=53.99s
6. max_window_size=7, kernel_sizes=3,7,11, reduction_ratio=16, dev_wer=196.02%, time=52.79s
7. max_window_size=11, kernel_sizes=3,7,11, reduction_ratio=16, dev_wer=196.26%, time=54.55s
8. max_window_size=7, kernel_sizes=5,9,13, reduction_ratio=32, dev_wer=196.80%, time=53.62s
9. max_window_size=7, kernel_sizes=3,5,7, reduction_ratio=8, dev_wer=196.91%, time=58.14s
10. max_window_size=9, kernel_sizes=3,7,11, reduction_ratio=8, dev_wer=197.41%, time=54.12s
11. max_window_size=9, kernel_sizes=3,5,7, reduction_ratio=8, dev_wer=197.52%, time=53.60s
12. max_window_size=9, kernel_sizes=5,9,13, reduction_ratio=32, dev_wer=197.73%, time=54.62s
13. max_window_size=9, kernel_sizes=5,9,13, reduction_ratio=8, dev_wer=197.76%, time=54.36s
14. max_window_size=11, kernel_sizes=5,9,13, reduction_ratio=8, dev_wer=197.79%, time=53.54s
15. max_window_size=7, kernel_sizes=3,5,7, reduction_ratio=16, dev_wer=198.00%, time=52.62s
16. max_window_size=11, kernel_sizes=3,7,11, reduction_ratio=8, dev_wer=198.00%, time=53.85s
17. max_window_size=7, kernel_sizes=3,5,7, reduction_ratio=32, dev_wer=198.03%, time=52.93s
18. max_window_size=9, kernel_sizes=3,5,7, reduction_ratio=16, dev_wer=198.05%, time=53.60s
19. max_window_size=11, kernel_sizes=3,5,7, reduction_ratio=8, dev_wer=198.53%, time=53.94s
20. max_window_size=7, kernel_sizes=3,7,11, reduction_ratio=32, dev_wer=198.59%, time=53.11s
21. max_window_size=11, kernel_sizes=5,9,13, reduction_ratio=32, dev_wer=198.67%, time=54.02s
22. max_window_size=9, kernel_sizes=5,9,13, reduction_ratio=16, dev_wer=198.67%, time=54.17s
23. max_window_size=11, kernel_sizes=3,5,7, reduction_ratio=32, dev_wer=198.80%, time=54.10s
24. max_window_size=9, kernel_sizes=3,7,11, reduction_ratio=16, dev_wer=198.80%, time=53.46s
25. max_window_size=9, kernel_sizes=3,7,11, reduction_ratio=32, dev_wer=199.12%, time=52.40s
26. max_window_size=9, kernel_sizes=3,5,7, reduction_ratio=32, dev_wer=199.25%, time=53.57s
27. max_window_size=11, kernel_sizes=3,7,11, reduction_ratio=32, dev_wer=199.47%, time=54.19s
